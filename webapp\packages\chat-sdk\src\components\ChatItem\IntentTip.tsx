import React, { useEffect, useState } from 'react';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import classNames from 'classnames';
import { PREFIX_CLS } from '../../common/constants';
import Loading from './Loading';

const prefixCls = `${PREFIX_CLS}-item`;

type Props = {
  data_query_type: string;
  onComplete?: () => void;
  parseInfo: any;
  errorMsg?: string; // 新增：错误信息
  failed?: boolean; // 新增：是否失败
  enableThinkingChain?: boolean; // 新增：是否启用思维链动画
};

const IntentTip: React.FC<Props> = ({
  data_query_type,
  onComplete,
  parseInfo,
  errorMsg,
  failed = false,
  enableThinkingChain = true
}) => {
  const [isCompleted, setIsCompleted] = useState(!enableThinkingChain || failed);
  const [hasTriggeredComplete, setHasTriggeredComplete] = useState(false);

  // 当props变化时重置状态
  useEffect(() => {
    setIsCompleted(!enableThinkingChain || failed);
    setHasTriggeredComplete(false);
  }, [data_query_type, enableThinkingChain, failed]);

  useEffect(() => {
    // 防止重复触发
    if (hasTriggeredComplete) {
      return;
    }

    if (failed || !enableThinkingChain) {
      // 失败状态或不启用思维链时立即完成
      setHasTriggeredComplete(true);
      if (onComplete) {
        onComplete();
      }
      return;
    }

    // 启用思维链时，1秒后显示完成状态
    const timer = setTimeout(() => {
      setIsCompleted(true);
      setHasTriggeredComplete(true);
      if (onComplete) {
        onComplete();
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [failed, enableThinkingChain]); // 移除onComplete依赖，防止重复执行

  // 判断是否为意图分类失败（通过data_query_type或failed参数）
  const isIntentFailed = failed || data_query_type === '没有识别到用户意图';

  return (
    <div className={classNames(`${prefixCls}-parse-tip`, isIntentFailed && `${prefixCls}-parse-tip-failed`)}>
      <div className={`${prefixCls}-title-bar`}>
        {!isIntentFailed ? (
          isCompleted ? (
            <CheckCircleFilled className={`${prefixCls}-step-icon`} />
          ) : (
            <Loading />
          )
        ) : (
          <CloseCircleFilled className={`${prefixCls}-step-error-icon`} />
        )}
        <div className={`${prefixCls}-step-title`}>
          意图分类{isIntentFailed ? '失败' : isCompleted ? '' : '中'}
        </div>
      </div>
      {isCompleted && (
        <div className={classNames(
          `${prefixCls}-content-container`,
          isIntentFailed && `${prefixCls}-content-container-failed`
        )}>
          <div className={`${prefixCls}-tip`}>
            <div className={`${prefixCls}-tip-content`}>
              <div className={`${prefixCls}-tip-item`}>
                {isIntentFailed ? (
                  <>
                    没有识别到用户意图，请尝试重新描述您的问题
                    {errorMsg && (
                      <div style={{ marginTop: 8, color: '#ff4d4f', fontSize: '12px' }}>
                        错误详情：{errorMsg}
                      </div>
                    )}
                  </>
                ) : (
                  `用户的问题属于${data_query_type}`
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default IntentTip;
